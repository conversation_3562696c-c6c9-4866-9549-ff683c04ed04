import React, { useState, useEffect } from "react";
import Layout from "../Layout/Layout";
import axios from "axios";
import userAPI from "../../services/userAPI";
import Swal from 'sweetalert2';
import "../../../src/App.css";
import "../DietaryPreferences/DietaryPreferences.css";

const Family = () => {
  const [user, setUser] = useState(null);

  const [members, setMembers] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [editingMember, setEditingMember] = useState(null);
  const [form, setForm] = useState({
    name: "",
    dateOfBirth: "",
    dietaryPreferences: {
      restrictions: [],
      allergies: [],
      dislikedIngredients: []
    }
  });

  // Custom allergy states
  const [customAllergy, setCustomAllergy] = useState('');
  const [showCustomAllergyInput, setShowCustomAllergyInput] = useState(false);

  // Custom restriction states
  const [customRestriction, setCustomRestriction] = useState('');
  const [showCustomRestrictionInput, setShowCustomRestrictionInput] = useState(false);

  // Dietary options (matching main dietary preferences screen)
  const dietaryOptions = [
    'Vegetarian',
    'Vegan',
    'Gluten-Free',
    'Dairy-Free',
    'Nut-Free',
    'Low-Carb',
    'Keto',
    'Pescatarian',
    'Halal',
    'Paleo',
    'Mediterranean',
    'Plant-Based',
    'Organic',
    'High-Protein',
    'Low-Sodium',
    'Sugar-Free',
    'Kosher',
    'Pollotarian',
    'Flexitarian',
    'None'
  ];

  const allergyOptions = [
    'Milk',
    'Eggs',
    'Fish',
    'Shellfish',
    'Peanuts',
    'Tree Nuts',
    'Wheat',
    'Soybeans',
    'Sesame',
    'Mustard',
    'Celery',
    'Lupin',
    'Mollusks',
    'Sulfites',
    'None'
  ];

  // Toggle functions for multi-select
  const toggleRestriction = (restriction) => {
    setForm(prev => {
      if (restriction === 'None') {
        // If "None" is clicked, clear all other restrictions and set only "None"
        return {
          ...prev,
          dietaryPreferences: {
            ...prev.dietaryPreferences,
            restrictions: prev.dietaryPreferences.restrictions.includes('None') ? [] : ['None']
          }
        };
      } else {
        // If any other restriction is clicked, remove "None" if it exists
        const newRestrictions = prev.dietaryPreferences.restrictions.includes(restriction)
          ? prev.dietaryPreferences.restrictions.filter(r => r !== restriction)
          : [...prev.dietaryPreferences.restrictions.filter(r => r !== 'None'), restriction];

        return {
          ...prev,
          dietaryPreferences: {
            ...prev.dietaryPreferences,
            restrictions: newRestrictions
          }
        };
      }
    });
  };

  const toggleAllergy = (allergy) => {
    setForm(prev => {
      if (allergy === 'None') {
        // If "None" is clicked, clear all other allergies and set only "None"
        return {
          ...prev,
          dietaryPreferences: {
            ...prev.dietaryPreferences,
            allergies: prev.dietaryPreferences.allergies.includes('None') ? [] : ['None']
          }
        };
      } else {
        // If any other allergy is clicked, remove "None" if it exists
        const newAllergies = prev.dietaryPreferences.allergies.includes(allergy)
          ? prev.dietaryPreferences.allergies.filter(a => a !== allergy)
          : [...prev.dietaryPreferences.allergies.filter(a => a !== 'None'), allergy];

        return {
          ...prev,
          dietaryPreferences: {
            ...prev.dietaryPreferences,
            allergies: newAllergies
          }
        };
      }
    });
  };

  const addDislikedIngredient = (ingredient) => {
    console.log('🥗 Adding disliked ingredient:', ingredient);
    if (ingredient.trim() && !form.dietaryPreferences.dislikedIngredients.includes(ingredient.trim())) {
      const newIngredients = [...form.dietaryPreferences.dislikedIngredients, ingredient.trim()];
      console.log('🥗 Updated disliked ingredients:', newIngredients);
      setForm(prev => ({
        ...prev,
        dietaryPreferences: {
          ...prev.dietaryPreferences,
          dislikedIngredients: newIngredients
        }
      }));
    } else {
      console.log('🥗 Ingredient not added - either empty or already exists');
    }
  };

  const removeDislikedIngredient = (ingredient) => {
    console.log('🗑️ Removing disliked ingredient:', ingredient);
    const newIngredients = form.dietaryPreferences.dislikedIngredients.filter(i => i !== ingredient);
    console.log('🗑️ Updated disliked ingredients after removal:', newIngredients);
    setForm(prev => ({
      ...prev,
      dietaryPreferences: {
        ...prev.dietaryPreferences,
        dislikedIngredients: newIngredients
      }
    }));
  };

  // Fetch logged-in user info
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const token = localStorage.getItem("token");
        if (!token) return;
        const res = await axios.get("http://localhost:5000/api/users/profile", {
          headers: { Authorization: `Bearer ${token}` },
        });
        setUser(res.data);
      } catch (err) {
        setUser(null);
      }
    };
    fetchUser();
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
  };

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return null;

    const today = new Date();
    const birthDate = new Date(dateOfBirth);

    if (isNaN(birthDate.getTime())) return null;

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Get age-based color for family member avatar
  const getAgeBasedColor = (dateOfBirth) => {
    const age = calculateAge(dateOfBirth);

    if (age === null) {
      return '#8E8E93'; // Default color for unknown age (iOS gray)
    }

    if (age < 13) {
      return '#FF3B30'; // Vibrant red for children (0-12)
    } else if (age < 20) {
      return '#30D158'; // Vibrant green for teenagers (13-19)
    } else if (age < 35) {
      return '#007AFF'; // Vibrant blue for young adults (20-34)
    } else if (age < 55) {
      return '#FF9500'; // Vibrant orange for adults (35-54)
    } else {
      return '#AF52DE'; // Vibrant purple for seniors (55+)
    }
  };

const handleAddMember = async (e) => {
  e.preventDefault();

  if (!form.name.trim()) {
    alert("Please enter a name for the family member.");
    return;
  }

  // Validate that at least one dietary preference is provided
  const restrictions = form.dietaryPreferences.restrictions;
  const allergies = form.dietaryPreferences.allergies;
  const dislikedIngredients = form.dietaryPreferences.dislikedIngredients;

  if (restrictions.length === 0 && allergies.length === 0 && dislikedIngredients.length === 0) {
    alert("Please provide at least one dietary restriction, allergy, or disliked ingredient for the family member. This helps us provide better meal recommendations.");
    return;
  }

  // If editing, call update function
  if (editingMember) {
    await handleUpdateMember();
    return;
  }

  // Otherwise, add new member
  try {
    const memberData = {
      name: form.name,
      dateOfBirth: form.dateOfBirth || null,
      dietaryPreferences: {
        restrictions: form.dietaryPreferences.restrictions || [],
        allergies: form.dietaryPreferences.allergies || [],
        dislikedIngredients: form.dietaryPreferences.dislikedIngredients || [],
        calorieTarget: null,
        macroTargets: {
          protein: null,
          carbs: null,
          fat: null
        },
        mealFrequency: 3
      },
    };
    console.log('🔍 Form state before submission:', JSON.stringify(form, null, 2));
    console.log('🔍 Adding family member with data:', JSON.stringify(memberData, null, 2));
    const response = await userAPI.addFamilyMember(memberData);
    console.log('🔍 Add family member result:', JSON.stringify(response, null, 2));

    if (response.success) {
      // Refresh family members from server to ensure UI shows latest data
      console.log('✅ Family member added successfully, refreshing data...');
      await fetchFamilyMembers();

      // Reset form
      setForm({
        name: "",
        dateOfBirth: "",
        dietaryPreferences: {
          restrictions: [],
          allergies: [],
          dislikedIngredients: []
        }
      });
      setShowForm(false);
      alert("Family member added successfully!");
    } else {
      alert(response.error || "Failed to add family member.");
    }
  } catch (err) {
    console.error("Add family member error:", err);
    alert("Failed to add family member.");
  }
};

const handleRemoveMember = async (memberId) => {
  const token = localStorage.getItem("token");
  if (!token) return;

  // Show SweetAlert2 confirmation dialog
  const result = await Swal.fire({
    title: 'Are you sure?',
    text: "Do you want to remove this family member?",
    icon: 'warning',
    showCancelButton: true,
    confirmButtonColor: '#d33',
    cancelButtonColor: '#3085d6',
    confirmButtonText: 'Yes, remove',
    cancelButtonText: 'Cancel'
  });
  
  // If user clicks Cancel, return without doing anything
  if (!result.isConfirmed) {
    return;
  }

  try {
    await axios.delete(
      `http://localhost:5000/api/users/family-members/${memberId}`,
      { headers: { Authorization: `Bearer ${token}` } }
    );

    // Refresh family members from server to ensure UI shows latest data
    console.log('✅ Family member removed successfully, refreshing data...');
    await fetchFamilyMembers();

    // Show success message
    await Swal.fire({
      title: 'Removed!',
      text: 'Family member has been removed successfully.',
      icon: 'success',
      timer: 2000,
      showConfirmButton: false
    });
  } catch (err) {
    // Show error message
    await Swal.fire({
      title: 'Error!',
      text: 'Failed to remove family member.',
      icon: 'error',
      confirmButtonColor: '#3085d6'
    });
  }
};

// Edit family member
const handleEditMember = (member) => {
  setEditingMember(member);
  setForm({
    name: member.name || "",
    dateOfBirth: member.dateOfBirth ? member.dateOfBirth.split('T')[0] : "",
    dietaryPreferences: {
      restrictions: member.dietaryPreferences?.restrictions || [],
      allergies: member.dietaryPreferences?.allergies || [],
      dislikedIngredients: member.dietaryPreferences?.dislikedIngredients || []
    }
  });
  setShowForm(true);
};

// Update family member
const handleUpdateMember = async () => {
  if (!editingMember) return;

  try {
    const memberData = {
      name: form.name,
      dateOfBirth: form.dateOfBirth || null,
      dietaryPreferences: {
        restrictions: form.dietaryPreferences.restrictions || [],
        allergies: form.dietaryPreferences.allergies || [],
        dislikedIngredients: form.dietaryPreferences.dislikedIngredients || [],
        calorieTarget: form.dietaryPreferences.calorieTarget || null,
        macroTargets: {
          protein: null,
          carbs: null,
          fat: null
        },
        mealFrequency: 3
      },
    };

    console.log('🔍 DETAILED UPDATE DEBUG:');
    console.log('🔍 Editing member ID:', editingMember._id);
    console.log('🔍 Editing member name:', editingMember.name);
    console.log('🔍 Form state before update:', JSON.stringify(form, null, 2));
    console.log('🔍 Member data being sent:', JSON.stringify(memberData, null, 2));
    console.log('🔍 Disliked ingredients specifically:', memberData.dietaryPreferences.dislikedIngredients);

    const response = await userAPI.updateFamilyMember(editingMember._id, memberData);
    console.log('🔍 Update family member API response:', JSON.stringify(response, null, 2));

    if (response.success) {
      // Refresh family members from server to ensure UI shows latest data
      console.log('✅ Family member updated successfully, refreshing data...');
      console.log('🔄 Current members state before refresh:', members.length, 'members');

      await fetchFamilyMembers();

      console.log('🔄 Data refresh completed');

      // Reset form and close edit mode
      setForm({
        name: "",
        dateOfBirth: "",
        dietaryPreferences: {
          restrictions: [],
          allergies: [],
          dislikedIngredients: []
        }
      });
      setShowForm(false);
      setEditingMember(null);
      alert("Family member updated successfully!");
    } else {
      console.log('❌ Update failed:', response.error);
      alert(response.error || "Failed to update family member.");
    }
  } catch (err) {
    console.error("Update family member error:", err);
    alert("Failed to update family member.");
  }
};

// Cancel edit
const handleCancelEdit = () => {
  setEditingMember(null);
  setForm({
    name: "",
    dateOfBirth: "",
    dietaryPreferences: {
      restrictions: [],
      allergies: [],
      dislikedIngredients: []
    }
  });
  setShowForm(false);
};

// Migration mapping for old allergy names to new standardized names
const allergyMigrationMap = {
  'Nuts': 'Tree Nuts',
  'Gluten': 'Wheat',
  'Soy': 'Soybeans',
  'Dairy': 'Milk'
};

// Function to migrate old allergy names to new ones
const migrateAllergies = (allergies) => {
  return allergies.map(allergy => allergyMigrationMap[allergy] || allergy);
};

// Custom allergy functions
const addCustomAllergy = () => {
  if (customAllergy.trim()) {
    // Split by comma and add multiple allergies
    const newAllergies = customAllergy.split(',').map(a => a.trim()).filter(a => a && !form.dietaryPreferences.allergies.includes(a));
    if (newAllergies.length > 0) {
      setForm(prev => ({
        ...prev,
        dietaryPreferences: {
          ...prev.dietaryPreferences,
          allergies: [...prev.dietaryPreferences.allergies, ...newAllergies]
        }
      }));
      setCustomAllergy('');
      setShowCustomAllergyInput(false);
    }
  }
};

const removeCustomAllergy = (allergy) => {
  setForm(prev => {
    const updatedAllergies = prev.dietaryPreferences.allergies.filter(a => a !== allergy);
    const customAllergies = updatedAllergies.filter(a => !allergyOptions.includes(a));

    // Update the custom allergy text field
    setCustomAllergy(customAllergies.join(', '));

    // Hide the input if no custom allergies remain
    if (customAllergies.length === 0) {
      setShowCustomAllergyInput(false);
    }

    return {
      ...prev,
      dietaryPreferences: {
        ...prev.dietaryPreferences,
        allergies: updatedAllergies
      }
    };
  });
};

// Custom restriction functions
const addCustomRestriction = () => {
  if (customRestriction.trim()) {
    // Split by comma and add multiple restrictions
    const newRestrictions = customRestriction.split(',').map(r => r.trim()).filter(r => r && !form.dietaryPreferences.restrictions.includes(r));
    if (newRestrictions.length > 0) {
      setForm(prev => ({
        ...prev,
        dietaryPreferences: {
          ...prev.dietaryPreferences,
          restrictions: [...prev.dietaryPreferences.restrictions, ...newRestrictions]
        }
      }));
      setCustomRestriction('');
      setShowCustomRestrictionInput(false);
    }
  }
};

const removeCustomRestriction = (restriction) => {
  setForm(prev => {
    const updatedRestrictions = prev.dietaryPreferences.restrictions.filter(r => r !== restriction);
    const customRestrictions = updatedRestrictions.filter(r => !dietaryOptions.includes(r));

    // Update the custom restriction text field
    setCustomRestriction(customRestrictions.join(', '));

    // Hide the input if no custom restrictions remain
    if (customRestrictions.length === 0) {
      setShowCustomRestrictionInput(false);
    }

    return {
      ...prev,
      dietaryPreferences: {
        ...prev.dietaryPreferences,
        restrictions: updatedRestrictions
      }
    };
  });
};

// Check if there are custom allergies and set the "Other" option accordingly
useEffect(() => {
  const customAllergies = form.dietaryPreferences.allergies.filter(allergy => !allergyOptions.includes(allergy));
  if (customAllergies.length > 0) {
    setShowCustomAllergyInput(true);
    setCustomAllergy(customAllergies.join(', '));
  }
}, [form.dietaryPreferences.allergies]);

// Check if there are custom restrictions and set the "Other" option accordingly
useEffect(() => {
  const customRestrictions = form.dietaryPreferences.restrictions.filter(restriction => !dietaryOptions.includes(restriction));
  if (customRestrictions.length > 0) {
    setShowCustomRestrictionInput(true);
    setCustomRestriction(customRestrictions.join(', '));
  }
}, [form.dietaryPreferences.restrictions]);

// Fetch family members from backend
const fetchFamilyMembers = async () => {
  try {
    console.log('🔄 Fetching family members from server...');
    const response = await userAPI.getFamilyMembers();
    if (response.success) {
      console.log('📥 Raw response from server:', JSON.stringify(response.data, null, 2));

      // Migrate old allergy names for all family members
      const migratedMembers = (response.data || []).map(member => ({
        ...member,
        dietaryPreferences: {
          ...member.dietaryPreferences,
          allergies: migrateAllergies(member.dietaryPreferences?.allergies || [])
        }
      }));

      console.log('✅ Family members fetched successfully:', migratedMembers.length, 'members');

      // Log each member's disliked ingredients for debugging
      migratedMembers.forEach((member, index) => {
        console.log(`👤 Member ${index + 1}: ${member.name}`);
        console.log(`   - Disliked ingredients: ${JSON.stringify(member.dietaryPreferences?.dislikedIngredients)}`);
      });

      setMembers(migratedMembers);
    } else {
      console.error("Failed to fetch family members:", response.error);
      setMembers([]);
    }
  } catch (err) {
    console.error("Failed to fetch family members:", err);
    setMembers([]);
  }
};

useEffect(() => {
  fetchFamilyMembers();
}, []);

  return (
    <Layout>
      <div className="main-content">
        <div className="family-container-modern">
          <div className="family-header-modern">
            <div className="family-title-section">
              <h1>Family Profile</h1>
              <p className="family-subtitle">Manage your family members and their dietary preferences</p>
            </div>
            {user && (
              <div className="current-user-card">
                <div className="user-avatar">
                  {user.firstName?.charAt(0)}{user.lastName?.charAt(0)}
                </div>
                <div className="user-info">
                  <span className="user-name">{user.firstName} {user.lastName}</span>
                  <span className="user-role">Family Admin</span>
                </div>
              </div>
            )}
          </div>

          <div className="family-members-section">
            <div className="section-header">
              <div>
                <h2>Family Members</h2>
                <p className="section-subtitle">Manage dietary preferences for your family</p>
              </div>
              <button
                className="add-member-btn-modern"
                onClick={() => {
                  if (showForm && editingMember) {
                    handleCancelEdit();
                  } else {
                    setShowForm((prev) => !prev);
                  }
                }}
              >
                {showForm ? (
                  <>
                    <span>✕</span>
                    Cancel
                  </>
                ) : (
                  <>
                    <span>+</span>
                    Add Member
                  </>
                )}
              </button>
            </div>

            {showForm && (
              <div className="add-member-card">
                <div className="card-header">
                  <h3>{editingMember ? 'Edit Family Member' : 'Add New Family Member'}</h3>
                  <p className="card-subtitle">Enter their dietary information</p>
                </div>
                <form className="modern-form" onSubmit={handleAddMember}>
                  <div className="form-grid">
                    <div className="form-group">
                      <label className="form-label">
                        <span className="label-text">Name *</span>
                        <input
                          type="text"
                          name="name"
                          value={form.name}
                          onChange={handleInputChange}
                          required
                          className="form-input"
                          placeholder="Enter family member's name"
                        />
                      </label>
                    </div>
                    <div className="form-group">
                      <label className="form-label">
                        <span className="label-text">Date of Birth (Optional)</span>
                        <input
                          type="date"
                          name="dateOfBirth"
                          value={form.dateOfBirth}
                          onChange={handleInputChange}
                          className="form-input"
                          max={new Date().toISOString().split('T')[0]}
                        />
                        <small className="form-hint">Used to calculate age and provide age-appropriate meal suggestions</small>
                      </label>
                    </div>
                    {/* Dietary Restrictions */}
                    <div className="preference-section">
                      <h3>Dietary Restrictions <span className="required">* Required</span></h3>
                      <p>Select any dietary restrictions for this family member:</p>
                      <p className="learn-more-link">
                        Don't know what Dietary preferences mean? <a href="http://localhost:3000/help-center" target="_blank" rel="noopener noreferrer">Learn more</a>
                      </p>
                      <div className="options-grid">
                        {dietaryOptions.map((option) => (
                          <label key={option} className="option-checkbox">
                            <input
                              type="checkbox"
                              checked={form.dietaryPreferences.restrictions.includes(option)}
                              onChange={() => toggleRestriction(option)}
                            />
                            <span className="checkmark"></span>
                            {option}
                          </label>
                        ))}
                        {/* Other option */}
                        <label className="option-checkbox">
                          <input
                            type="checkbox"
                            checked={showCustomRestrictionInput || form.dietaryPreferences.restrictions.filter(restriction => !dietaryOptions.includes(restriction)).length > 0}
                            onChange={() => setShowCustomRestrictionInput(!showCustomRestrictionInput)}
                          />
                          <span className="checkmark"></span>
                          Other
                        </label>
                      </div>

                      {/* Custom restriction input */}
                      {showCustomRestrictionInput && (
                        <div className="custom-input-container">
                          <input
                            type="text"
                            className="custom-input"
                            placeholder="Enter custom dietary restriction (separate multiple with commas)"
                            value={customRestriction}
                            onChange={(e) => setCustomRestriction(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && addCustomRestriction()}
                          />
                          <button
                            type="button"
                            className="add-button"
                            onClick={addCustomRestriction}
                            disabled={!customRestriction.trim()}
                          >
                            Add
                          </button>
                        </div>
                      )}

                      {/* Display custom restrictions */}
                      {form.dietaryPreferences.restrictions.filter(restriction => !dietaryOptions.includes(restriction)).length > 0 && (
                        <div className="custom-items-display">
                          <h4>Custom Dietary Restrictions:</h4>
                          <div className="custom-items-list">
                            {form.dietaryPreferences.restrictions
                              .filter(restriction => !dietaryOptions.includes(restriction))
                              .map(restriction => (
                                <div key={restriction} className="custom-restriction-item">
                                  <span>{restriction}</span>
                                  <button
                                    type="button"
                                    className="remove-custom-restriction"
                                    onClick={() => removeCustomRestriction(restriction)}
                                    title="Remove restriction"
                                  >
                                    ×
                                  </button>
                                </div>
                              ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Allergies */}
                    <div className="preference-section">
                      <h3>Allergies</h3>
                      <p>Select any food allergies for this family member:</p>
                      <div className="options-grid">
                        {allergyOptions.map((option) => (
                          <label key={option} className="option-checkbox">
                            <input
                              type="checkbox"
                              checked={form.dietaryPreferences.allergies.includes(option)}
                              onChange={() => toggleAllergy(option)}
                            />
                            <span className="checkmark"></span>
                            {option}
                          </label>
                        ))}
                        {/* Other option */}
                        <label className="option-checkbox">
                          <input
                            type="checkbox"
                            checked={showCustomAllergyInput || form.dietaryPreferences.allergies.filter(allergy => !allergyOptions.includes(allergy)).length > 0}
                            onChange={() => setShowCustomAllergyInput(!showCustomAllergyInput)}
                          />
                          <span className="checkmark"></span>
                          Other
                        </label>
                      </div>

                      {/* Custom allergy input */}
                      {showCustomAllergyInput && (
                        <div className="custom-input-container">
                          <input
                            type="text"
                            className="custom-input"
                            placeholder="Enter custom allergy (separate multiple with commas)"
                            value={customAllergy}
                            onChange={(e) => setCustomAllergy(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && addCustomAllergy()}
                          />
                          <button
                            type="button"
                            className="add-button"
                            onClick={addCustomAllergy}
                            disabled={!customAllergy.trim()}
                          >
                            Add
                          </button>
                        </div>
                      )}

                      {/* Display custom allergies */}
                      {form.dietaryPreferences.allergies.filter(allergy => !allergyOptions.includes(allergy)).length > 0 && (
                        <div className="custom-allergies-container">
                          <h4>Custom Allergies:</h4>
                          <div className="custom-allergies-list">
                            {form.dietaryPreferences.allergies
                              .filter(allergy => !allergyOptions.includes(allergy))
                              .map(allergy => (
                                <div key={allergy} className="custom-allergy-item">
                                  <span>{allergy}</span>
                                  <button
                                    type="button"
                                    className="remove-custom-allergy"
                                    onClick={() => removeCustomAllergy(allergy)}
                                    title="Remove allergy"
                                  >
                                    ×
                                  </button>
                                </div>
                              ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {/* Disliked Ingredients */}
                    <div className="preference-section">
                      <h3>Disliked Ingredients</h3>
                      <p>Add any ingredients this family member dislikes:</p>
                      <div className="disliked-ingredients-input">
                        <input
                          type="text"
                          placeholder="Enter ingredient and press Enter"
                          className="form-input"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              addDislikedIngredient(e.target.value);
                              e.target.value = '';
                            }
                          }}
                        />
                      </div>
                      {form.dietaryPreferences.dislikedIngredients.length > 0 && (
                        <div className="disliked-ingredients-list">
                          {form.dietaryPreferences.dislikedIngredients.map((ingredient, index) => (
                            <div key={index} className="disliked-ingredient-item">
                              <span>{ingredient}</span>
                              <button
                                type="button"
                                className="remove-ingredient"
                                onClick={() => removeDislikedIngredient(ingredient)}
                              >
                                ×
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="form-actions">
                    <button type="submit" className="btn-primary-modern">
                      {editingMember ? 'Update Family Member' : 'Add Family Member'}
                    </button>
                    {editingMember && (
                      <button
                        type="button"
                        className="btn-secondary-modern"
                        onClick={handleCancelEdit}
                        style={{
                          marginLeft: '10px',
                          background: '#f8f9fa',
                          color: '#6c757d',
                          border: '1px solid #dee2e6',
                          borderRadius: '6px',
                          padding: '10px 20px',
                          cursor: 'pointer',
                          fontSize: '14px',
                          fontWeight: '500',
                          transition: 'all 0.2s ease'
                        }}
                        onMouseOver={(e) => {
                          e.target.style.background = '#e9ecef';
                          e.target.style.borderColor = '#adb5bd';
                        }}
                        onMouseOut={(e) => {
                          e.target.style.background = '#f8f9fa';
                          e.target.style.borderColor = '#dee2e6';
                        }}
                      >
                        Cancel Edit
                      </button>
                    )}
                  </div>
                </form>
              </div>
            )}

            {members.length === 0 ? (
              <div className="empty-state">
                <div className="empty-state-icon">👥</div>
                <h3>No family members yet</h3>
                <p>Add family members to manage their dietary preferences and create personalized meal plans.</p>
                <button
                  className="btn-primary-modern"
                  onClick={() => setShowForm(true)}
                >
                  Add Your First Member
                </button>
              </div>
            ) : (
              <>
                {/* Age Color Legend */}
                <div className="age-legend">
                  <h4>Age Color Guide:</h4>
                  <div className="age-legend-items">
                    <div className="age-legend-item">
                      <div className="age-legend-color" style={{ backgroundColor: '#FF3B30' }}></div>
                      <span>Children (0-12)</span>
                    </div>
                    <div className="age-legend-item">
                      <div className="age-legend-color" style={{ backgroundColor: '#30D158' }}></div>
                      <span>Teens (13-19)</span>
                    </div>
                    <div className="age-legend-item">
                      <div className="age-legend-color" style={{ backgroundColor: '#007AFF' }}></div>
                      <span>Young Adults (20-34)</span>
                    </div>
                    <div className="age-legend-item">
                      <div className="age-legend-color" style={{ backgroundColor: '#FF9500' }}></div>
                      <span>Adults (35-54)</span>
                    </div>
                    <div className="age-legend-item">
                      <div className="age-legend-color" style={{ backgroundColor: '#AF52DE' }}></div>
                      <span>Seniors (55+)</span>
                    </div>
                  </div>
                </div>

                <div className="family-members-grid">
                {members.map((member, idx) => (
                  <div key={member._id || idx} className="member-card">
                    <div className="member-header">
                      <div className="member-avatar" style={{
                        backgroundColor: getAgeBasedColor(member.dateOfBirth)
                      }}>
                        {member.name?.charAt(0)?.toUpperCase()}
                      </div>
                      <div className="member-info">
                        <h3 className="member-name">{member.name}</h3>
                        <span className="member-role">Family Member</span>
                        {member.dateOfBirth && calculateAge(member.dateOfBirth) && (
                          <span className="member-age">Age: {calculateAge(member.dateOfBirth)} years</span>
                        )}
                      </div>
                      <div className="member-actions" style={{ display: 'flex', alignItems: 'center' }}>
                        <button
                          className="edit-btn"
                          onClick={() => handleEditMember(member)}
                          title="Edit member"
                          style={{
                            background: '#007AFF',
                            color: 'white',
                            border: 'none',
                            borderRadius: '6px',
                            padding: '6px 12px',
                            marginRight: '8px',
                            cursor: 'pointer',
                            fontSize: '12px',
                            fontWeight: '500',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '4px',
                            transition: 'all 0.2s ease',
                            boxShadow: '0 2px 4px rgba(0, 122, 255, 0.2)'
                          }}
                          onMouseOver={(e) => {
                            e.target.style.background = '#0056CC';
                            e.target.style.transform = 'translateY(-1px)';
                          }}
                          onMouseOut={(e) => {
                            e.target.style.background = '#007AFF';
                            e.target.style.transform = 'translateY(0)';
                          }}
                        >
                          ✏️ Edit
                        </button>
                        <button
                          className="remove-btn"
                          onClick={() => handleRemoveMember(member._id)}
                          title="Remove member"
                        >
                          ✕
                        </button>
                      </div>
                    </div>
                    <div className="member-details">
                      {member.dateOfBirth && (
                        <div className="detail-item">
                          <span className="detail-label">Date of Birth</span>
                          <span className="detail-value">
                            {new Date(member.dateOfBirth).toLocaleDateString()}
                            {calculateAge(member.dateOfBirth) && (
                              <span className="age-display"> (Age: {calculateAge(member.dateOfBirth)} years)</span>
                            )}
                          </span>
                        </div>
                      )}
                      <div className="detail-item">
                        <span className="detail-label">Dietary Preferences</span>
                        <span className="detail-value">
                          {member.dietaryPreferences?.restrictions?.length
                            ? member.dietaryPreferences.restrictions.join(", ")
                            : "None specified"}
                        </span>
                      </div>
                      <div className="detail-item">
                        <span className="detail-label">Allergies</span>
                        <span className="detail-value">
                          {member.dietaryPreferences?.allergies?.length
                            ? member.dietaryPreferences.allergies.join(", ")
                            : "None specified"}
                        </span>
                      </div>
                      <div className="detail-item">
                        <span className="detail-label">Disliked Ingredients</span>
                        <span className="detail-value">
                          {(() => {
                            console.log('🔍 Displaying disliked ingredients for member:', member.name);
                            console.log('🔍 Member disliked ingredients:', member.dietaryPreferences?.dislikedIngredients);
                            console.log('🔍 Array length:', member.dietaryPreferences?.dislikedIngredients?.length);
                            return member.dietaryPreferences?.dislikedIngredients?.length
                              ? member.dietaryPreferences.dislikedIngredients.join(", ")
                              : "None specified";
                          })()}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              </>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Family;
